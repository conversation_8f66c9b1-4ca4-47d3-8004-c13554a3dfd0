/* Agent Network Graph Styling - Colors defined in src/index.css */

.agent-network-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 50%, #cbd5e1 100%);
  z-index: 1;
  overflow: hidden;
}

.agent-network-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.agent-network-svg {
  width: 100%;
  height: 100%;
  display: block;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.node {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.node:hover {
  filter: brightness(1.1);
  transition: filter 0.2s ease;
}

.node circle {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure all node elements are properly contained */
.nodes-container .node {
  transform-origin: center center;
}

.node .main-circle {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.node .progress-ring {
  transition: stroke-dasharray 0.6s cubic-bezier(0.4, 0, 0.2, 1),
              stroke 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center center;
  stroke-linecap: round;
}

.node .node-label {
  pointer-events: none;
  user-select: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-family: 'Montserrat', sans-serif;
  font-weight: bold;
  paint-order: stroke fill;
}

/* Enhanced Node Styling with Updated Color System */
.node {
  cursor: pointer;
  transition: all 200ms ease-in-out;
}

/* Main circle styling */
.node .main-circle {
  filter: drop-shadow(0 6px 12px rgba(0,0,0,0.15));
  transition: all 200ms ease-in-out;
}

/* Category nodes styling */
.node[data-type="category"] .main-circle {
  filter: drop-shadow(0 8px 16px rgba(0,0,0,0.2));
}

.node[data-type="category"] .node-label {
  font-size: 24px !important;
  font-weight: 700;
}

/* Progress ring styling */
.node .progress-ring {
  stroke-linecap: round;
  transition: all 400ms ease-in-out;
}

.node .progress-ring-bg {
  transition: all 400ms ease-in-out;
}

/* Text label styling */
.node .node-label {
  font-family: 'Montserrat', sans-serif;
  font-weight: bold;
  paint-order: stroke fill;
  transition: all 200ms ease-in-out;
}

/* Hover effects */
.node:hover .main-circle {
  filter: drop-shadow(0 8px 16px rgba(0,0,0,0.25));
}

.node[data-type="category"]:hover .main-circle {
  filter: drop-shadow(0 10px 20px rgba(0,0,0,0.3));
}

/* Fast container transitions */
.nodes-container {
  transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}



.node text {
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  user-select: none;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Tooltip styles */
.agent-tooltip {
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Animation for progress rings */
.node circle[stroke-dasharray] {
  transition: stroke-dasharray 0.5s ease;
  transform-origin: 0px 0px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .node text {
    font-size: 6px !important;
  }

  .node circle {
    r: 20px;
  }

  .node circle[r="40"] {
    r: 30px;
  }
}

/* Loading indicator styles */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #64748b;
}



.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* Refresh indicator */
.refresh-indicator {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 8px 16px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  color: #64748b;
  z-index: 10;
}

.refresh-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Agent detail panel */
.agent-detail-panel {
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif !important;
  animation: fadeInScale 0.2s ease-out;
}

.agent-detail-panel .close-button:hover {
  color: #374151 !important;
  transform: scale(1.1);
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .agent-detail-panel {
    max-width: 90vw !important;
    padding: 16px !important;
  }

  .refresh-indicator {
    top: 10px !important;
    right: 10px !important;
    padding: 6px 12px !important;
    font-size: 11px !important;
  }

  .agent-tooltip {
    font-size: 12px !important;
    padding: 6px 8px !important;
  }
}

@media (max-width: 480px) {
  .agent-detail-panel {
    max-width: 95vw !important;
    padding: 12px !important;
  }

  .agent-detail-panel h3 {
    font-size: 16px !important;
  }
}

/* Performance optimizations */
.agent-network-svg {
  will-change: transform;
}

.agent-network-svg circle {
  will-change: transform;
}

.agent-network-svg text {
  will-change: transform;
}

/* Full screen mode when shown alone */
.dashboard-main > .agent-network-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
}

/* Zoom Controls */
.zoom-controls {
  position: absolute;
  bottom: 120px; /* Move up to make room for brand tag */
  right: 24px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  z-index: 1000;
}

.zoom-button {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: none;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  transition: all 0.2s ease;
  font-family: 'Montserrat', sans-serif;
  user-select: none;
}

.zoom-button:hover {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
  color: #1f2937;
}

.zoom-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.zoom-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.zoom-button:disabled:hover {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: none;
}

.zoom-reset {
  width: 48px;
  height: 32px;
  border-radius: 16px;
  border: none;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
  transition: all 0.2s ease;
  font-family: 'Montserrat', sans-serif;
  user-select: none;
}

.zoom-reset:hover {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
  color: #1f2937;
}

.zoom-reset:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Animation for running agents */
.agent-node.running .progress-ring {
  animation: pulse-running 2s ease-in-out infinite;
}

@keyframes pulse-running {
  0%, 100% {
    stroke-width: 2px;
    stroke-opacity: 0.8;
  }
  50% {
    stroke-width: 4px;
    stroke-opacity: 1;
  }
}

/* Brand Tag */
.brand-tag {
  position: absolute;
  bottom: 0;
  right: 8px;
  margin-top: 16px;
  z-index: 1000;
}

.brand-tag-content {
  background: rgba(49, 52, 95, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px 12px 0 0;
  padding: 8px 12px;
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.brand-tag-content:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.powered-by-text {
  font-size: 10px;
  font-weight: 600;
  letter-spacing: 1px;
  color: rgba(255, 255, 255, 0.7);
  text-align: left;
}

.brand-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.brand-tag .brand-logo {
  height: 20px;
  width: auto;
  object-fit: contain;
}

.brand-title {
  font-size: 14px;
  font-weight: 700;
  margin: 0;
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 50%, #f472b6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 0.5px;
}

/* Mobile responsive zoom controls */
@media (max-width: 768px) {
  .zoom-controls {
    bottom: 100px; /* Move up to make room for brand tag on mobile */
    right: 16px;
    gap: 6px;
  }

  .zoom-button {
    width: 44px;
    height: 44px;
    font-size: 18px;
  }

  .zoom-reset {
    width: 44px;
    height: 28px;
    font-size: 11px;
  }

  .brand-tag {
    bottom: 16px;
    right: 16px;
    margin-top: 12px;
  }

  .brand-tag-content {
    padding: 10px 12px;
    gap: 6px;
  }

  .powered-by-text {
    font-size: 9px;
  }

  .brand-title {
    font-size: 12px;
  }

  .brand-tag .brand-logo {
    height: 16px;
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  .zoom-button, .zoom-reset {
    transform: none;
  }

  .zoom-button:active, .zoom-reset:active {
    transform: scale(0.95);
  }
}
