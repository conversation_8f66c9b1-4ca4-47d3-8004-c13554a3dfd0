/* Dashboard Styles */

.dashboard {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.dashboard-header {
  background: linear-gradient(90deg, #31345F 0%, #1E236A 100%);
  padding: 16px 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 10;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.brand-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.brand-logo {
  height: 32px;
  object-fit: cover;
}

.brand-text h1 {
  color: #ffffff;
  background: var(--Gradient, linear-gradient(88deg, #6C91F9 2.06%, #F46EFA 98.43%));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 20px;
  font-weight: 700;
  margin: 0;
}

.brand-text p {
  color: #cbd5e0;
  margin: 0;
  font-size: 14px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.user-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 14px;
}

.role-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.role-badge-super-admin {
  background: #fed7d7;
  color: #c53030;
}

.role-badge-brand-admin {
  background: #bee3f8;
  color: #2b6cb0;
}

.role-badge-editor {
  background: #c6f6d5;
  color: #2f855a;
}

.role-badge-operator {
  background: #faf089;
  color: #744210;
}

.role-badge-default {
  background: #e2e8f0;
  color: #4a5568;
}

.profile-button {
  background: #4299e1;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.profile-button:hover {
  background: #3182ce;
}

.logout-button {
  background: #e53e3e;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.logout-button:hover {
  background: #c53030;
}

.admin-button {
  background-color: #4a5568;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  margin-right: 8px;
  transition: background-color 0.2s;
}

.admin-button:hover {
  background-color: #2d3748;
}

/* Hamburger Menu Styles */
.hamburger-menu-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  position: relative;
  z-index: 1001;
}

.hamburger-menu-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.hamburger-icon {
  display: flex;
  flex-direction: column;
  width: 24px;
  height: 18px;
  justify-content: space-between;
}

.hamburger-icon span {
  display: block;
  height: 2px;
  width: 100%;
  background-color: #ffffff;
  border-radius: 1px;
  transition: all 0.3s ease;
}

.hamburger-menu {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.menu-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.menu-content {
  position: absolute;
  top: 0;
  right: 0;
  width: 320px;
  height: 100vh;
  background: white;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.menu-header {
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f7fafc;
}

.menu-header .user-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-start;
}

.menu-header .user-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 16px;
}

.menu-items {
  flex: 1;
  padding: 16px 0;
  display: flex;
  flex-direction: column;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  border: none;
  background: none;
  text-align: left;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: #4a5568;
}

.menu-item:hover {
  background-color: #f7fafc;
}

.menu-icon {
  font-size: 18px;
  width: 24px;
  text-align: center;
}

.admin-item:hover {
  background-color: #edf2f7;
  color: #2d3748;
}

.profile-item:hover {
  background-color: #ebf8ff;
  color: #2b6cb0;
}

.logout-item:hover {
  background-color: #fed7d7;
  color: #c53030;
}

/* New split-view layout styles */
.dashboard-main {
  flex: 1;
  display: flex;
  padding: 0;
  width: 100%;
  overflow: hidden;
  position: relative;
}

.split-container {
  flex: 1;
  display: flex;
  height: calc(100vh - 80px); /* Adjust based on header height */
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  padding: 16px;
  gap: 16px;
  position: relative;
  z-index: 10;
}

.chat-section {
  flex: 0 0 33.333%; /* 1/3 of the container */
  display: flex;
  flex-direction: column;
  min-width: 0; /* Allow shrinking */
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.json-output-section {
  flex: 0 0 66.667%; /* 2/3 of the container */
  display: flex;
  flex-direction: column;
  min-width: 0; /* Allow shrinking */
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.json-output-placeholder {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  border: 2px dashed #e2e8f0;
}

.json-output-placeholder h3 {
  color: #4a5568;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 8px 0;
  font-family: 'Montserrat', sans-serif;
}

.json-output-placeholder p {
  color: #718096;
  font-size: 14px;
  margin: 0;
  font-family: 'Montserrat', sans-serif;
}

/* User Profile Styles */
.user-profile {
  padding: 24px;
  width: 100%;
}

.dashboard-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.welcome-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.welcome-section h2 {
  color: #1a202c;
  font-size: 20px;
  font-weight: 700;
  margin: 0 0 8px 0;
}

.welcome-section p {
  color: #4a5568;
  margin: 0;
  line-height: 1.5;
}

.user-profile-card,
.permissions-card,
.quick-actions,
.role-info {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.user-profile-card h3,
.permissions-card h3,
.quick-actions h3,
.role-info h3 {
  color: #1a202c;
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.profile-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.profile-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.profile-item label {
  font-weight: 600;
  color: #4a5568;
  font-size: 14px;
}

.profile-item span {
  color: #1a202c;
  font-size: 16px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  width: fit-content;
}

.status-active {
  background: #c6f6d5;
  color: #2f855a;
}

.status-inactive {
  background: #fed7d7;
  color: #c53030;
}

.permissions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.permission-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f7fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.permission-icon {
  color: #38a169;
  font-weight: bold;
}

.permission-name {
  font-size: 12px;
  font-weight: 600;
  color: #2d3748;
}

/* Responsive design */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }

  .split-container {
    flex-direction: column;
    padding: 8px;
    gap: 8px;
    height: calc(100vh - 80px); /* Keep consistent header height */
  }

  .chat-section {
    flex: 0 0 50%; /* Take up half the height on mobile */
  }

  .json-output-section {
    flex: 0 0 50%; /* Take up half the height on mobile */
  }

  .profile-details {
    grid-template-columns: 1fr;
  }

  .permissions-grid {
    grid-template-columns: 1fr;
  }

  /* Mobile hamburger menu adjustments */
  .menu-content {
    width: 100vw;
    max-width: 320px;
  }

  .hamburger-menu-button {
    padding: 12px;
  }

  .hamburger-icon {
    width: 20px;
    height: 16px;
  }
}
