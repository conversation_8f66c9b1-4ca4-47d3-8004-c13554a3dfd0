import React, { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import axios from 'axios';
import './AgentNetworkGraph.css';

interface AgentNode {
  id: string;
  name: string;
  category: string;
  type: 'category' | 'agent';
  status: 'idle' | 'running' | 'completed' | 'error';
  progress: number; // 0-100
  x?: number;
  y?: number;
  fx?: number | null;
  fy?: number | null;
}

interface AgentLink {
  source: string | AgentNode;
  target: string | AgentNode;
}

interface NetworkData {
  categories: Array<{
    id: string;
    name: string;
    status: string;
    progress: number;
  }>;
  agents: Array<{
    id: string;
    name: string;
    category: string;
    status: string;
    progress: number;
  }>;
}

const AgentNetworkGraph: React.FC = () => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [dimensions, setDimensions] = useState({ width: 800, height: 600 });
  const [animationFrame, setAnimationFrame] = useState(0);
  const [networkData, setNetworkData] = useState<NetworkData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch network data from API
  const fetchNetworkData = React.useCallback(async (isInitialLoad = false) => {
    try {
      if (isInitialLoad) {
        setLoading(true);
      } else {
        setRefreshing(true);
      }
      const response = await axios.get('/api/agents/network');
      if (response.data.success) {
        setNetworkData(response.data.data);
      }
    } catch (error) {
      console.error('Failed to fetch network data:', error);
      // Fallback to static data if API fails
      if (isInitialLoad) {
        setNetworkData({
          categories: [
            { id: 'business-strategy', name: 'Business Strategy & Brand Differentiation', status: 'idle', progress: 0 },
            { id: 'omnichannel', name: 'Omnichannel & Transformation', status: 'running', progress: 45 },
            { id: 'content-creation', name: 'Content Creation & Delivery', status: 'idle', progress: 0 }
          ],
          agents: [
            { id: 'clinical-profile', name: 'Clinical Profile Agent', category: 'business-strategy', status: 'idle', progress: 0 },
            { id: 'therapeutic-overview', name: 'Therapeutic Overview', category: 'business-strategy', status: 'completed', progress: 100 },
            { id: 'market-research', name: 'Market Research Agent', category: 'business-strategy', status: 'running', progress: 65 },
            { id: 'product-profile', name: 'Product Profile Agent', category: 'business-strategy', status: 'completed', progress: 100 },
            { id: 'customer-profile', name: 'Customer Profile Agent', category: 'business-strategy', status: 'idle', progress: 0 },
            { id: 'brand-positioning', name: 'Brand Positioning Agent', category: 'business-strategy', status: 'error', progress: 25 }
          ]
        });
      }
    } finally {
      if (isInitialLoad) {
        setLoading(false);
      } else {
        setRefreshing(false);
      }
    }
  }, []);

  // Initial data fetch
  useEffect(() => {
    fetchNetworkData(true);
  }, [fetchNetworkData]);

  // Set up periodic polling for real-time updates with visibility check
  useEffect(() => {
    let interval: NodeJS.Timeout;

    const startPolling = () => {
      interval = setInterval(() => {
        // Only poll if the page is visible to save resources
        if (!document.hidden) {
          fetchNetworkData(false);
        }
      }, 5000); // Poll every 5 seconds
    };

    const handleVisibilityChange = () => {
      if (document.hidden) {
        clearInterval(interval);
      } else {
        startPolling();
        // Fetch immediately when page becomes visible
        fetchNetworkData(false);
      }
    };

    startPolling();
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      clearInterval(interval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [fetchNetworkData]);

  // Convert network data to agent nodes
  const agentData: AgentNode[] = React.useMemo(() => {
    if (!networkData) return [];

    const categoryNodes: AgentNode[] = networkData.categories.map(cat => ({
      id: cat.id,
      name: cat.name,
      category: cat.id,
      type: 'category' as const,
      status: cat.status as any,
      progress: cat.progress
    }));

    const agentNodes: AgentNode[] = networkData.agents.map(agent => ({
      id: agent.id,
      name: agent.name,
      category: agent.category,
      type: 'agent' as const,
      status: agent.status as any,
      progress: agent.progress
    }));

    return [...categoryNodes, ...agentNodes];
  }, [networkData]);

  // Create links between category nodes and their agents
  const links: AgentLink[] = React.useMemo(() => {
    if (!networkData) return [];

    return networkData.categories.flatMap(category =>
      agentData.filter(d => d.category === category.id && d.type === 'agent')
        .map(d => ({ source: category.id, target: d.id }))
    );
  }, [agentData, networkData]);

  // Color schemes for different categories
  const getNodeColor = (node: AgentNode) => {
    const colors = {
      'business-strategy': '#10B981', // Green
      'omnichannel': '#3B82F6',      // Blue
      'content-creation': '#F59E0B'   // Orange
    };
    return colors[node.category as keyof typeof colors] || '#6B7280';
  };

  // Status color mapping
  const getStatusColor = (status: string) => {
    const colors = {
      'idle': '#6B7280',
      'running': '#3B82F6',
      'completed': '#10B981',
      'error': '#EF4444'
    };
    return colors[status as keyof typeof colors] || '#6B7280';
  };



  // Animation effect for running agents
  useEffect(() => {
    const interval = setInterval(() => {
      setAnimationFrame(prev => prev + 1);
    }, 2000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const handleResize = () => {
      const container = svgRef.current?.parentElement;
      if (container) {
        const width = container.clientWidth;
        const height = container.clientHeight;

        // Ensure minimum dimensions for usability
        setDimensions({
          width: Math.max(width, 400),
          height: Math.max(height, 300)
        });
      }
    };

    // Use ResizeObserver for better performance if available
    let resizeObserver: ResizeObserver | null = null;

    if (window.ResizeObserver && svgRef.current?.parentElement) {
      resizeObserver = new ResizeObserver(handleResize);
      resizeObserver.observe(svgRef.current.parentElement);
    } else {
      // Fallback to window resize event
      window.addEventListener('resize', handleResize);
    }

    handleResize();

    return () => {
      if (resizeObserver) {
        resizeObserver.disconnect();
      } else {
        window.removeEventListener('resize', handleResize);
      }
    };
  }, []);

  useEffect(() => {
    if (!svgRef.current || loading || agentData.length === 0) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const { width, height } = dimensions;
    
    // Create simulation with better forces for horizontal grouping
    const simulation = d3.forceSimulation(agentData)
      .force('link', d3.forceLink(links).id((d: any) => d.id).distance(80))
      .force('charge', d3.forceManyBody().strength((d: any) => d.type === 'category' ? -800 : -200))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius((d: any) => d.type === 'category' ? 60 : 35))
      .force('x', d3.forceX().strength(0.1))
      .force('y', d3.forceY().strength(0.1))
      .alphaDecay(0.02) // Slower decay for smoother animation
      .velocityDecay(0.8); // Higher velocity decay for stability

    // Create container group
    const container = svg.append('g');

    // Add zoom behavior
    const zoom = d3.zoom()
      .scaleExtent([0.5, 3])
      .on('zoom', (event) => {
        container.attr('transform', event.transform);
      });

    svg.call(zoom as any);

    // Add click-to-close functionality for detail panels
    svg.on('click', () => {
      d3.selectAll('.agent-detail-panel').remove();
    });

    // Create links
    const link = container.append('g')
      .selectAll('line')
      .data(links)
      .enter().append('line')
      .attr('stroke', '#E5E7EB')
      .attr('stroke-width', 2)
      .attr('stroke-opacity', 0.6);

    // Create node groups
    const node = container.append('g')
      .selectAll('g')
      .data(agentData)
      .enter().append('g')
      .attr('class', 'node')
      .style('cursor', 'pointer');

    // Add drop shadow filter
    const defs = svg.append('defs');
    const filter = defs.append('filter')
      .attr('id', 'drop-shadow')
      .attr('x', '-50%')
      .attr('y', '-50%')
      .attr('width', '200%')
      .attr('height', '200%');

    filter.append('feDropShadow')
      .attr('dx', 2)
      .attr('dy', 2)
      .attr('stdDeviation', 3)
      .attr('flood-opacity', 0.3);

    // Add circles for nodes
    node.append('circle')
      .attr('r', (d: AgentNode) => d.type === 'category' ? 40 : 25)
      .attr('fill', (d: AgentNode) => getNodeColor(d))
      .attr('stroke', '#fff')
      .attr('stroke-width', 2)
      .attr('filter', 'url(#drop-shadow)')
      .attr('class', (d: AgentNode) => d.status === 'running' ? 'pulsing' : '');

    // Add pulsing animation for running agents
    const style = svg.append('style');
    style.text(`
      .pulsing {
        animation: pulse 2s infinite;
      }
      @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.6; }
        100% { opacity: 1; }
      }
    `);

    // Add progress rings
    node.append('circle')
      .attr('r', (d: AgentNode) => d.type === 'category' ? 42 : 27)
      .attr('fill', 'none')
      .attr('stroke', (d: AgentNode) => getStatusColor(d.status))
      .attr('stroke-width', 3)
      .attr('stroke-dasharray', (d: AgentNode) => {
        const radius = d.type === 'category' ? 42 : 27;
        const circumference = 2 * Math.PI * radius;
        const progress = circumference * (d.progress / 100);
        return `${progress} ${circumference - progress}`;
      })
      .attr('stroke-dashoffset', 0)
      .style('transform', 'rotate(-90deg)')
      .style('transform-origin', 'center');

    // Add text labels
    node.append('text')
      .text((d: AgentNode) => {
        // Truncate long names for better display
        const maxLength = d.type === 'category' ? 12 : 8;
        return d.name.length > maxLength ? d.name.substring(0, maxLength) + '...' : d.name;
      })
      .attr('text-anchor', 'middle')
      .attr('dy', '0.35em')
      .attr('font-size', (d: AgentNode) => d.type === 'category' ? '10px' : '8px')
      .attr('font-weight', (d: AgentNode) => d.type === 'category' ? 'bold' : 'normal')
      .attr('fill', 'white')
      .style('pointer-events', 'none');

    // Add drag behavior
    const drag = d3.drag<SVGGElement, AgentNode>()
      .on('start', (event, d) => {
        if (!event.active) simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
      })
      .on('drag', (event, d) => {
        d.fx = event.x;
        d.fy = event.y;
      })
      .on('end', (event, d) => {
        if (!event.active) simulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;
      });

    node.call(drag);

    // Add hover effects
    node.on('mouseover', function(event, d) {
      d3.select(this).select('circle').attr('stroke-width', 4);
      
      // Show tooltip
      const tooltip = d3.select('body').append('div')
        .attr('class', 'agent-tooltip')
        .style('opacity', 0)
        .style('position', 'absolute')
        .style('background', 'rgba(0, 0, 0, 0.8)')
        .style('color', 'white')
        .style('padding', '8px')
        .style('border-radius', '4px')
        .style('font-size', '12px')
        .style('pointer-events', 'none')
        .style('z-index', '1000');

      tooltip.transition()
        .duration(200)
        .style('opacity', 1);

      tooltip.html(`
        <strong>${d.name}</strong><br/>
        Status: ${d.status}<br/>
        Progress: ${d.progress}%
      `)
        .style('left', (event.pageX + 10) + 'px')
        .style('top', (event.pageY - 10) + 'px');
    })
    .on('mouseout', function() {
      d3.select(this).select('circle').attr('stroke-width', 2);
      d3.selectAll('.agent-tooltip').remove();
    })
    .on('click', function(event, d) {
      event.stopPropagation();

      // Remove any existing detail panels
      d3.selectAll('.agent-detail-panel').remove();

      // Create detailed information panel
      const panel = d3.select('body').append('div')
        .attr('class', 'agent-detail-panel')
        .style('position', 'fixed')
        .style('top', '50%')
        .style('left', '50%')
        .style('transform', 'translate(-50%, -50%)')
        .style('background', 'white')
        .style('border-radius', '12px')
        .style('box-shadow', '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)')
        .style('padding', '24px')
        .style('max-width', '400px')
        .style('z-index', '1000')
        .style('font-family', 'Montserrat, sans-serif');

      // Add close button
      panel.append('button')
        .attr('class', 'close-button')
        .style('position', 'absolute')
        .style('top', '12px')
        .style('right', '12px')
        .style('background', 'none')
        .style('border', 'none')
        .style('font-size', '20px')
        .style('cursor', 'pointer')
        .style('color', '#64748b')
        .text('×')
        .on('click', () => panel.remove());

      // Add agent details
      panel.append('h3')
        .style('margin', '0 0 16px 0')
        .style('color', '#1e293b')
        .text(d.name);

      panel.append('div')
        .style('margin-bottom', '12px')
        .html(`<strong>Category:</strong> ${d.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}`);

      panel.append('div')
        .style('margin-bottom', '12px')
        .html(`<strong>Status:</strong> <span style="color: ${getStatusColor(d.status)}">${d.status.toUpperCase()}</span>`);

      panel.append('div')
        .style('margin-bottom', '16px')
        .html(`<strong>Progress:</strong> ${d.progress}%`);

      // Add progress bar
      const progressContainer = panel.append('div')
        .style('background', '#e2e8f0')
        .style('border-radius', '4px')
        .style('height', '8px')
        .style('margin-bottom', '16px');

      progressContainer.append('div')
        .style('background', getStatusColor(d.status))
        .style('height', '100%')
        .style('border-radius', '4px')
        .style('width', d.progress + '%')
        .style('transition', 'width 0.3s ease');

      // Add type-specific information
      if (d.type === 'category') {
        panel.append('p')
          .style('color', '#64748b')
          .style('font-size', '14px')
          .style('margin', '0')
          .text('This is a category node that groups related agents together.');
      } else {
        panel.append('p')
          .style('color', '#64748b')
          .style('font-size', '14px')
          .style('margin', '0')
          .text('Click and drag to move this agent. The visualization updates in real-time.');
      }
    });

    // Update positions on simulation tick
    simulation.on('tick', () => {
      link
        .attr('x1', (d: any) => d.source.x)
        .attr('y1', (d: any) => d.source.y)
        .attr('x2', (d: any) => d.target.x)
        .attr('y2', (d: any) => d.target.y);

      node
        .attr('transform', (d: AgentNode) => `translate(${d.x},${d.y})`);
    });

    // Position category nodes horizontally after initial simulation
    setTimeout(() => {
      const categoryNodes = agentData.filter(d => d.type === 'category');
      const spacing = width / (categoryNodes.length + 1);

      categoryNodes.forEach((node, i) => {
        node.fx = spacing * (i + 1);
        node.fy = height / 2;
      });

      // Position agent nodes around their category centers
      agentData.filter(d => d.type === 'agent').forEach(agent => {
        const categoryNode = categoryNodes.find(cat => cat.id === agent.category);
        if (categoryNode) {
          const categoryIndex = categoryNodes.indexOf(categoryNode);
          const categoryX = spacing * (categoryIndex + 1);

          // Add some randomness around the category center
          const angle = Math.random() * 2 * Math.PI;
          const distance = 80 + Math.random() * 60;
          agent.fx = categoryX + Math.cos(angle) * distance;
          agent.fy = height / 2 + Math.sin(angle) * distance;
        }
      });

      simulation.alpha(0.5).restart();

      // Release fixed positions after a short time to allow natural movement
      setTimeout(() => {
        agentData.forEach(node => {
          if (node.type === 'agent') {
            node.fx = null;
            node.fy = null;
          }
        });
      }, 2000);
    }, 1000);

  }, [dimensions, agentData, links, loading, animationFrame]);

  if (loading) {
    return (
      <div className="agent-network-container">
        <div className="loading-indicator">
          <div className="loading-spinner"></div>
          <p>Loading Agent Network...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="agent-network-container">
      {refreshing && (
        <div className="refresh-indicator">
          <div className="refresh-spinner"></div>
          <span>Updating...</span>
        </div>
      )}
      <svg
        ref={svgRef}
        width={dimensions.width}
        height={dimensions.height}
        className="agent-network-svg"
      />
    </div>
  );
};

export default AgentNetworkGraph;
