:root {
  font-family: '<PERSON><PERSON><PERSON>', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Light Theme Status Colors */
  --status-active: 142 76% 45%; /* Warm green */
  --status-idle: 197 92% 61%; /* Soft sky blue */
  --status-error: 25 95% 53%; /* Warm orange */
  --status-disabled: 220 14% 71%; /* Soft gray */

  /* UI colors */
  --border: 229 231 235; /* Light Gray border */
  --muted: 156 163 175; /* Muted gray */
}

/* Dark Theme Status Colors */
@media (prefers-color-scheme: dark) {
  :root {
    --status-active: 142 71% 50%; /* Slightly brighter warm green */
    --status-idle: 197 92% 65%; /* Slightly brighter sky blue */
    --status-error: 25 95% 58%; /* Slightly brighter orange */
    --status-disabled: 220 14% 65%; /* Adjusted gray for dark mode */
  }
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

html, body {
  margin: 0;
  padding: 0;
  min-width: 320px;
  min-height: 100vh;
  width: 100%;
  overflow: hidden; /* Prevent scrolling */
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}
