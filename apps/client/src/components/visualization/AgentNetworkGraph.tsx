import React, { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import axios from 'axios';
import './AgentNetworkGraph.css';

interface AgentNode {
  id: string;
  name: string;
  category: string;
  type: 'category' | 'agent';
  status: 'idle' | 'running' | 'completed' | 'error';
  progress: number; // 0-100
  x?: number;
  y?: number;
  fx?: number | null;
  fy?: number | null;
}

interface AgentLink {
  source: string | AgentNode;
  target: string | AgentNode;
}

interface NetworkData {
  categories: Array<{
    id: string;
    name: string;
    status: string;
    progress: number;
  }>;
  agents: Array<{
    id: string;
    name: string;
    category: string;
    status: string;
    progress: number;
  }>;
}

interface AgentNetworkGraphProps {
  onNodeClick?: (nodeData: AgentNode) => void;
}

const AgentNetworkGraph: React.FC<AgentNetworkGraphProps> = ({ onNodeClick }) => {
  const svgRef = useRef<SVGSVGElement>(null);
  const zoomRef = useRef<any>(null);
  const [dimensions, setDimensions] = useState({
    width: window.innerWidth,
    height: window.innerHeight
  });
  const [networkData, setNetworkData] = useState<NetworkData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [isVisible, setIsVisible] = useState(false);
  const [simulationStable, setSimulationStable] = useState(false);

  // Fetch network data from API
  const fetchNetworkData = React.useCallback(async (isInitialLoad = false) => {
    try {
      if (isInitialLoad) {
        setLoading(true);
      } else {
        setRefreshing(true);
      }
      const response = await axios.get('/api/agents/network');
      if (response.data.success) {
        // Only update state if data has actually changed
        setNetworkData(prevData => {
          if (JSON.stringify(prevData) === JSON.stringify(response.data.data)) {
            return prevData; // Return same reference to prevent re-render
          }
          return response.data.data;
        });
      }
    } catch (error) {
      console.error('Failed to fetch network data:', error);
      // Fallback to static data if API fails
      if (isInitialLoad) {
        setNetworkData({
          categories: [
            { id: 'business-strategy', name: 'Business Strategy & Brand Differentiation', status: 'idle', progress: 0 },
            { id: 'omnichannel', name: 'Omnichannel & Transformation', status: 'running', progress: 45 },
            { id: 'content-creation', name: 'Content Creation & Delivery', status: 'idle', progress: 0 }
          ],
          agents: [
            { id: 'clinical-profile', name: 'Clinical Profile Agent', category: 'business-strategy', status: 'idle', progress: 0 },
            { id: 'therapeutic-overview', name: 'Therapeutic Overview', category: 'business-strategy', status: 'completed', progress: 100 },
            { id: 'market-research', name: 'Market Research Agent', category: 'business-strategy', status: 'running', progress: 65 },
            { id: 'product-profile', name: 'Product Profile Agent', category: 'business-strategy', status: 'completed', progress: 100 },
            { id: 'customer-profile', name: 'Customer Profile Agent', category: 'business-strategy', status: 'idle', progress: 0 },
            { id: 'brand-positioning', name: 'Brand Positioning Agent', category: 'business-strategy', status: 'error', progress: 25 }
          ]
        });
      }
    } finally {
      if (isInitialLoad) {
        setLoading(false);
      } else {
        setRefreshing(false);
      }
    }
  }, []);

  // Initial data fetch
  useEffect(() => {
    fetchNetworkData(true);
  }, [fetchNetworkData]);

  // Set up periodic polling for real-time updates with visibility check
  useEffect(() => {
    let interval: NodeJS.Timeout;

    const startPolling = () => {
      interval = setInterval(() => {
        // Only poll if the page is visible to save resources
        if (!document.hidden) {
          fetchNetworkData(false);
        }
      }, 10000); // Poll every 10 seconds to reduce re-renders
    };

    const handleVisibilityChange = () => {
      if (document.hidden) {
        clearInterval(interval);
      } else {
        startPolling();
        // Fetch immediately when page becomes visible
        fetchNetworkData(false);
      }
    };

    startPolling();
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      clearInterval(interval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [fetchNetworkData]);

  // Convert network data to agent nodes
  const agentData: AgentNode[] = React.useMemo(() => {
    if (!networkData) return [];

    const categoryNodes: AgentNode[] = networkData.categories.map(cat => ({
      id: cat.id,
      name: cat.name,
      category: cat.id,
      type: 'category' as const,
      status: cat.status as any,
      progress: cat.progress
    }));

    const agentNodes: AgentNode[] = networkData.agents.map(agent => ({
      id: agent.id,
      name: agent.name,
      category: agent.category,
      type: 'agent' as const,
      status: agent.status as any,
      progress: agent.progress
    }));

    return [...categoryNodes, ...agentNodes];
  }, [networkData]);

  // Create links between category nodes and their agents
  const links: AgentLink[] = React.useMemo(() => {
    if (!networkData) return [];

    return networkData.agents.map(agent => ({
      source: agent.category,
      target: agent.id
    }));
  }, [networkData]);

  // Status color mapping using CSS custom properties
  const getStatusColor = (status: string) => {
    const colors = {
      'active': 'hsl(var(--status-active))',
      'idle': 'hsl(var(--status-idle))',
      'error': 'hsl(var(--status-error))',
      'disabled': 'hsl(var(--status-disabled))'
    };
    return colors[status as keyof typeof colors] || 'hsl(var(--status-idle))';
  };

  // Get node fill color based on status
  const getNodeFillColor = (node: AgentNode) => {
    return node.status === 'active' ? getStatusColor(node.status) : '#ffffff';
  };

  // Get text color based on status
  const getTextColor = (node: AgentNode) => {
    return node.status === 'active' ? '#ffffff' : '#000000';
  };

  // Get progress percentage based on status
  const getProgressPercentage = (status: string) => {
    const progressMap = {
      'idle': 30,
      'error': 60,
      'disabled': 10,
      'active': 0 // Active agents don't show progress
    };
    return progressMap[status as keyof typeof progressMap] || 0;
  };

  // Calculate dynamic font size based on name length
  const getFontSize = (name: string, radius: number = 60) => {
    const nameLength = name.replace(' Agent', '').length;
    const calculatedSize = Math.min(radius / nameLength * 2.5, 21);
    return Math.max(calculatedSize, 18);
  };

  // Zoom control functions
  const handleZoomIn = () => {
    if (zoomRef.current && zoomLevel < 3) {
      const svg = d3.select(svgRef.current);
      const newScale = Math.min(zoomLevel * 1.5, 3);
      const transform = d3.zoomIdentity.scale(newScale);
      svg.transition().duration(300).call(zoomRef.current.transform, transform);
      setZoomLevel(newScale);
    }
  };

  const handleZoomOut = () => {
    if (zoomRef.current && zoomLevel > 0.5) {
      const svg = d3.select(svgRef.current);
      const newScale = Math.max(zoomLevel / 1.5, 0.5);
      const transform = d3.zoomIdentity.scale(newScale);
      svg.transition().duration(300).call(zoomRef.current.transform, transform);
      setZoomLevel(newScale);
    }
  };

  const handleZoomReset = () => {
    if (zoomRef.current) {
      const svg = d3.select(svgRef.current);
      const transform = d3.zoomIdentity;
      svg.transition().duration(500).call(zoomRef.current.transform, transform);
      setZoomLevel(1);
    }
  };

  // Handle smooth updates when data changes
  useEffect(() => {
    if (!networkData || !svgRef.current || loading) return;

    const svg = d3.select(svgRef.current);
    const existingNodes = svg.selectAll('.node');

    if (existingNodes.size() > 0 && isVisible) {
      // Update progress rings smoothly
      existingNodes.selectAll('.progress-ring')
        .transition()
        .duration(600)
        .ease(d3.easeCircleOut)
        .attr('stroke-dasharray', (d: any) => {
          const radius = 70; // Progress ring radius
          const circumference = 2 * Math.PI * radius;
          const progressPercentage = getProgressPercentage(d.status);
          const progress = circumference * (progressPercentage / 100);
          return `${progress} ${circumference - progress}`;
        })
        .attr('stroke', (d: any) => getStatusColor(d.status));

      // Update node colors smoothly
      existingNodes.selectAll('.main-circle')
        .transition()
        .duration(400)
        .ease(d3.easeCircleOut)
        .attr('fill', (d: any) => getNodeFillColor(d));
    }
  }, [networkData, isVisible]);





  useEffect(() => {
    const handleResize = () => {
      setDimensions({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    window.addEventListener('resize', handleResize);
    handleResize();

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    if (!svgRef.current || loading || agentData.length === 0) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    // Reset visibility states
    setIsVisible(false);
    setSimulationStable(false);

    const { width, height } = dimensions;
    
    // Create simulation with forces optimized for new node design
    const simulation = d3.forceSimulation(agentData)
      .force('link', d3.forceLink(links).id((d: any) => d.id).distance(120).strength(0.3))
      .force('charge', d3.forceManyBody().strength(-1000)) // Repulsion force
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius(105)) // 105px collision radius to prevent overlap
      .force('x', d3.forceX().strength(0.1))
      .force('y', d3.forceY().strength(0.1))
      .alphaDecay(0.02) // Slower decay for smoother animation
      .velocityDecay(0.8); // Higher velocity decay for stability

    // Create container group with minimal initial scaling for fast render
    const container = svg.append('g')
      .style('opacity', 0.1)
      .style('transform', 'scale(0.98)');

    // Add zoom behavior
    const zoom = d3.zoom()
      .scaleExtent([0.5, 3])
      .on('zoom', (event) => {
        container.attr('transform', event.transform);
        setZoomLevel(event.transform.k);
      });

    svg.call(zoom as any);
    zoomRef.current = zoom;

    // Add click-to-close functionality for detail panels
    svg.on('click', () => {
      d3.selectAll('.agent-detail-panel').remove();
    });

    // Create links
    const link = container.append('g')
      .selectAll('line')
      .data(links)
      .enter().append('line')
      .attr('stroke', '#E5E7EB')
      .attr('stroke-width', 2)
      .attr('stroke-opacity', 0.6);

    // Create node groups
    const node = container.append('g')
      .attr('class', 'nodes-container')
      .selectAll('g')
      .data(agentData)
      .enter().append('g')
      .attr('class', (d: AgentNode) => `node agent-node ${d.status === 'running' ? 'running' : ''}`)
      .style('cursor', 'pointer');

    // Add drop shadow filter
    const defs = svg.append('defs');
    const filter = defs.append('filter')
      .attr('id', 'drop-shadow')
      .attr('x', '-50%')
      .attr('y', '-50%')
      .attr('width', '200%')
      .attr('height', '200%');

    filter.append('feDropShadow')
      .attr('dx', 2)
      .attr('dy', 2)
      .attr('stdDeviation', 3)
      .attr('flood-opacity', 0.3);

    // Create structured node elements for each node
    node.each(function(d: AgentNode) {
      const nodeGroup = d3.select(this);
      const radius = 60; // Base radius for all agent nodes
      const progressRadius = 70; // Progress ring radius (10px larger)
      const progressPercentage = getProgressPercentage(d.status);

      // Add progress ring background (only for non-active agents)
      if (d.status !== 'active' && progressPercentage > 0) {
        nodeGroup.append('circle')
          .attr('class', 'progress-ring-bg')
          .attr('cx', 0)
          .attr('cy', 0)
          .attr('r', progressRadius)
          .attr('fill', 'none')
          .attr('stroke', 'hsl(var(--muted))')
          .attr('stroke-width', 12)
          .attr('stroke-opacity', 0.3);

        // Add progress arc
        nodeGroup.append('circle')
          .attr('class', 'progress-ring')
          .attr('cx', 0)
          .attr('cy', 0)
          .attr('r', progressRadius)
          .attr('fill', 'none')
          .attr('stroke', getStatusColor(d.status))
          .attr('stroke-width', 12)
          .attr('stroke-dasharray', () => {
            const circumference = 2 * Math.PI * progressRadius;
            const progress = circumference * (progressPercentage / 100);
            return `${progress} ${circumference - progress}`;
          })
          .attr('stroke-dashoffset', 0)
          .attr('transform', 'rotate(-90)')
          .attr('stroke-linecap', 'round')
          .style('border-radius', '3px');
      }

      // Add main circle
      nodeGroup.append('circle')
        .attr('class', 'main-circle')
        .attr('cx', 0)
        .attr('cy', 0)
        .attr('r', radius)
        .attr('fill', getNodeFillColor(d))
        .attr('stroke', 'hsl(var(--border))')
        .attr('stroke-width', 3)
        .attr('filter', 'url(#drop-shadow)');
    });

    // Add pulsing animation for running agents
    const style = svg.append('style');
    style.text(`
      .pulsing {
        animation: pulse 2s infinite;
      }
      @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.6; }
        100% { opacity: 1; }
      }
    `);

    // Add text labels to each node
    node.each(function(d: AgentNode) {
      const nodeGroup = d3.select(this);
      const cleanName = d.name.replace(' Agent', ''); // Remove " Agent" suffix
      const fontSize = getFontSize(cleanName);
      const textColor = getTextColor(d);
      const strokeColor = d.status === 'active' ? getStatusColor(d.status) : '#ffffff';

      // Split text into multiple lines if needed
      const words = cleanName.split(' ');
      const lineHeight = 1.1;

      if (words.length === 1) {
        // Single word - center it
        nodeGroup.append('text')
          .attr('class', 'node-label')
          .attr('x', 0)
          .attr('y', 0)
          .text(cleanName)
          .attr('text-anchor', 'middle')
          .attr('dy', '0.35em')
          .attr('font-size', `${fontSize}px`)
          .attr('font-weight', 'bold')
          .attr('fill', textColor)
          .attr('stroke', strokeColor)
          .attr('stroke-width', '3px')
          .attr('paint-order', 'stroke fill')
          .style('pointer-events', 'none');
      } else {
        // Multiple words - split across lines
        const textGroup = nodeGroup.append('g').attr('class', 'text-group');
        const yOffset = -(words.length - 1) * fontSize * lineHeight / 2;

        words.forEach((word, i) => {
          textGroup.append('text')
            .attr('class', 'node-label')
            .attr('x', 0)
            .attr('y', yOffset + i * fontSize * lineHeight)
            .text(word)
            .attr('text-anchor', 'middle')
            .attr('dy', '0.35em')
            .attr('font-size', `${fontSize}px`)
            .attr('font-weight', 'bold')
            .attr('fill', textColor)
            .attr('stroke', strokeColor)
            .attr('stroke-width', '3px')
            .attr('paint-order', 'stroke fill')
            .style('pointer-events', 'none');
        });
      }
    });

    // Add drag behavior
    const drag = d3.drag<SVGGElement, AgentNode>()
      .on('start', (event, d) => {
        if (!event.active) simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
      })
      .on('drag', (event, d) => {
        d.fx = event.x;
        d.fy = event.y;
      })
      .on('end', (event, d) => {
        if (!event.active) simulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;
      });

    node.call(drag);

    // Add hover effects
    node.on('mouseover', function(event, d) {
      // Expand main circle radius on hover
      d3.select(this).select('.main-circle')
        .transition()
        .duration(200)
        .attr('r', 67); // Expand from 60px to 67px

      // Show tooltip
      const tooltip = d3.select('body').append('div')
        .attr('class', 'agent-tooltip')
        .style('opacity', 0)
        .style('position', 'absolute')
        .style('background', 'rgba(0, 0, 0, 0.8)')
        .style('color', 'white')
        .style('padding', '8px')
        .style('border-radius', '4px')
        .style('font-size', '12px')
        .style('pointer-events', 'none')
        .style('z-index', '1000');

      tooltip.transition()
        .duration(200)
        .style('opacity', 1);

      tooltip.html(`
        <strong>${d.name}</strong><br/>
        Status: ${d.status}<br/>
        Progress: ${d.progress}%
      `)
        .style('left', (event.pageX + 10) + 'px')
        .style('top', (event.pageY - 10) + 'px');
    })
    .on('mouseout', function() {
      // Restore main circle radius on mouseout
      d3.select(this).select('.main-circle')
        .transition()
        .duration(200)
        .attr('r', 60); // Restore to original 60px
      d3.selectAll('.agent-tooltip').remove();
    })
    .on('click', function(event, d) {
      event.stopPropagation();

      // If onNodeClick prop is provided, call it instead of showing detail panel
      if (onNodeClick) {
        onNodeClick(d);
        return;
      }

      // Fallback to original detail panel behavior if no onNodeClick prop
      // Remove any existing detail panels
      d3.selectAll('.agent-detail-panel').remove();

      // Create detailed information panel
      const panel = d3.select('body').append('div')
        .attr('class', 'agent-detail-panel')
        .style('position', 'fixed')
        .style('top', '50%')
        .style('left', '50%')
        .style('transform', 'translate(-50%, -50%)')
        .style('background', 'white')
        .style('border-radius', '12px')
        .style('box-shadow', '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)')
        .style('padding', '24px')
        .style('max-width', '400px')
        .style('z-index', '1000')
        .style('font-family', 'Montserrat, sans-serif');

      // Add close button
      panel.append('button')
        .attr('class', 'close-button')
        .style('position', 'absolute')
        .style('top', '12px')
        .style('right', '12px')
        .style('background', 'none')
        .style('border', 'none')
        .style('font-size', '20px')
        .style('cursor', 'pointer')
        .style('color', '#64748b')
        .text('×')
        .on('click', () => panel.remove());

      // Add agent details
      panel.append('h3')
        .style('margin', '0 0 16px 0')
        .style('color', '#1e293b')
        .text(d.name);

      panel.append('div')
        .style('margin-bottom', '12px')
        .html(`<strong>Category:</strong> ${d.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}`);

      panel.append('div')
        .style('margin-bottom', '12px')
        .html(`<strong>Status:</strong> <span style="color: ${getStatusColor(d.status)}">${d.status.toUpperCase()}</span>`);

      panel.append('div')
        .style('margin-bottom', '16px')
        .html(`<strong>Progress:</strong> ${d.progress}%`);

      // Add progress bar
      const progressContainer = panel.append('div')
        .style('background', '#e2e8f0')
        .style('border-radius', '4px')
        .style('height', '8px')
        .style('margin-bottom', '16px');

      progressContainer.append('div')
        .style('background', getStatusColor(d.status))
        .style('height', '100%')
        .style('border-radius', '4px')
        .style('width', d.progress + '%')
        .style('transition', 'width 0.3s ease');

      // Add type-specific information
      if (d.type === 'category') {
        panel.append('p')
          .style('color', '#64748b')
          .style('font-size', '14px')
          .style('margin', '0')
          .text('This is a category node that groups related agents together.');
      } else {
        panel.append('p')
          .style('color', '#64748b')
          .style('font-size', '14px')
          .style('margin', '0')
          .text('Click and drag to move this agent. The visualization updates in real-time.');
      }
    });

    // Update positions on simulation tick with stability tracking
    let tickCount = 0;
    simulation.on('tick', () => {
      tickCount++;

      link
        .attr('x1', (d: any) => d.source.x)
        .attr('y1', (d: any) => d.source.y)
        .attr('x2', (d: any) => d.target.x)
        .attr('y2', (d: any) => d.target.y);

      node
        .attr('transform', (d: AgentNode) => `translate(${d.x},${d.y})`);

      // Show the visualization quickly after minimal positioning
      if (tickCount > 3 && !simulationStable) {
        setSimulationStable(true);
        // Fast fade-in and scale-up animation
        container
          .transition()
          .duration(300)
          .ease(d3.easeCircleOut)
          .style('opacity', 1)
          .style('transform', 'scale(1)')
          .on('end', () => {
            setIsVisible(true);
          });
      }
    });

    // Set better initial positions to reduce janky movements
    const categoryNodes = agentData.filter(d => d.type === 'category');
    const spacing = width / (categoryNodes.length + 1);

    // Set initial positions immediately (before simulation starts)
    categoryNodes.forEach((node, i) => {
      node.x = spacing * (i + 1);
      node.y = height * 0.4;
      node.fx = spacing * (i + 1);
      node.fy = height * 0.4;
    });

    // Position agent nodes around their category centers
    agentData.filter(d => d.type === 'agent').forEach(agent => {
      const categoryNode = categoryNodes.find(cat => cat.id === agent.category);
      if (categoryNode) {
        const categoryIndex = categoryNodes.indexOf(categoryNode);
        const categoryX = spacing * (categoryIndex + 1);

        // Add some randomness around the category center
        const angle = Math.random() * 2 * Math.PI;
        const distance = 80 + Math.random() * 60;
        agent.x = categoryX + Math.cos(angle) * distance;
        agent.y = height * 0.4 + Math.sin(angle) * distance;
      }
    });

    // Allow natural movement after quick stabilization
    setTimeout(() => {
      categoryNodes.forEach(node => {
        node.fx = null;
        node.fy = null;
      });

      simulation.alpha(0.1).restart();
    }, 1000);

  }, [dimensions, agentData, links, loading]);

  if (loading) {
    return (
      <div className="agent-network-container">
        <div className="loading-indicator">
          <div className="loading-spinner"></div>
          <p>Loading Agent Network...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="agent-network-container">
      {refreshing && (
        <div className="refresh-indicator">
          <div className="refresh-spinner"></div>
          <span>Updating...</span>
        </div>
      )}



      <svg
        ref={svgRef}
        width={dimensions.width}
        height={dimensions.height}
        className="agent-network-svg"
      />
      <div className="zoom-controls">
        <button
          className="zoom-button"
          onClick={handleZoomIn}
          disabled={zoomLevel >= 3}
          title="Zoom In"
        >
          +
        </button>
        <button
          className="zoom-reset"
          onClick={handleZoomReset}
          title={`Reset Zoom (Current: ${Math.round(zoomLevel * 100)}%)`}
        >
          {Math.round(zoomLevel * 100)}%
        </button>
        <button
          className="zoom-button"
          onClick={handleZoomOut}
          disabled={zoomLevel <= 0.5}
          title="Zoom Out"
        >
          −
        </button>
      </div>

      {/* Branded tag button */}
      <div className="brand-tag">
        <div className="brand-tag-content">
          <span className="powered-by-text">POWERED BY</span>
          <div className="brand-info">
            <img src="/Eversana Logo.svg" alt="Eversana Logo" className="brand-logo" />
            <h1 className="brand-title">AI AGENCY</h1>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgentNetworkGraph;
