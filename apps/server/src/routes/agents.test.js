const request = require('supertest');
const express = require('express');
const agentsRouter = require('./agents');

const app = express();
app.use('/api/agents', agentsRouter);

describe('Agents API', () => {
  describe('GET /api/agents/network', () => {
    it('should return network data with categories and agents', async () => {
      const response = await request(app)
        .get('/api/agents/network')
        .expect(200);

      expect(response.body).toHaveProperty('success', true);
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('categories');
      expect(response.body.data).toHaveProperty('agents');
    });

    it('should return three categories', async () => {
      const response = await request(app)
        .get('/api/agents/network')
        .expect(200);

      const { categories } = response.body.data;
      expect(categories).toHaveLength(3);
      
      const categoryIds = categories.map(cat => cat.id);
      expect(categoryIds).toContain('business-strategy');
      expect(categoryIds).toContain('omnichannel');
      expect(categoryIds).toContain('content-creation');
    });

    it('should return agents with required properties', async () => {
      const response = await request(app)
        .get('/api/agents/network')
        .expect(200);

      const { agents } = response.body.data;
      expect(agents.length).toBeGreaterThan(0);
      
      agents.forEach(agent => {
        expect(agent).toHaveProperty('id');
        expect(agent).toHaveProperty('name');
        expect(agent).toHaveProperty('category');
        expect(agent).toHaveProperty('status');
        expect(agent).toHaveProperty('progress');
        expect(typeof agent.progress).toBe('number');
        expect(agent.progress).toBeGreaterThanOrEqual(0);
        expect(agent.progress).toBeLessThanOrEqual(100);
      });
    });

    it('should return categories with required properties', async () => {
      const response = await request(app)
        .get('/api/agents/network')
        .expect(200);

      const { categories } = response.body.data;
      
      categories.forEach(category => {
        expect(category).toHaveProperty('id');
        expect(category).toHaveProperty('name');
        expect(category).toHaveProperty('status');
        expect(category).toHaveProperty('progress');
        expect(typeof category.progress).toBe('number');
        expect(category.progress).toBeGreaterThanOrEqual(0);
        expect(category.progress).toBeLessThanOrEqual(100);
      });
    });

    it('should return valid status values', async () => {
      const response = await request(app)
        .get('/api/agents/network')
        .expect(200);

      const validStatuses = ['idle', 'running', 'completed', 'error'];
      const { categories, agents } = response.body.data;
      
      [...categories, ...agents].forEach(item => {
        expect(validStatuses).toContain(item.status);
      });
    });

    it('should return agents categorized correctly', async () => {
      const response = await request(app)
        .get('/api/agents/network')
        .expect(200);

      const { categories, agents } = response.body.data;
      const categoryIds = categories.map(cat => cat.id);
      
      agents.forEach(agent => {
        expect(categoryIds).toContain(agent.category);
      });
    });
  });
});
