import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import axios from 'axios'
import AgentNetworkGraph from './AgentNetworkGraph'

// Mock axios
vi.mock('axios')
const mockedAxios = vi.mocked(axios)

// Mock D3 modules
vi.mock('d3', () => ({
  select: vi.fn(() => ({
    selectAll: vi.fn(() => ({
      remove: vi.fn(),
    })),
    append: vi.fn(() => ({
      attr: vi.fn(() => ({
        attr: vi.fn(() => ({
          attr: vi.fn(() => ({
            attr: vi.fn(() => ({
              attr: vi.fn(() => ({
                attr: vi.fn(() => ({
                  attr: vi.fn(() => ({
                    text: vi.fn(),
                  })),
                })),
              })),
            })),
          })),
        })),
      })),
      call: vi.fn(),
      on: vi.fn(),
    })),
  })),
  forceSimulation: vi.fn(() => ({
    force: vi.fn(() => ({
      force: vi.fn(() => ({
        force: vi.fn(() => ({
          force: vi.fn(() => ({
            force: vi.fn(() => ({
              force: vi.fn(() => ({
                alphaDecay: vi.fn(() => ({
                  velocityDecay: vi.fn(),
                })),
              })),
            })),
          })),
        })),
      })),
    })),
    on: vi.fn(),
  })),
  forceLink: vi.fn(() => ({
    id: vi.fn(() => ({
      distance: vi.fn(),
    })),
  })),
  forceManyBody: vi.fn(() => ({
    strength: vi.fn(),
  })),
  forceCenter: vi.fn(),
  forceCollide: vi.fn(() => ({
    radius: vi.fn(),
  })),
  forceX: vi.fn(() => ({
    strength: vi.fn(),
  })),
  forceY: vi.fn(() => ({
    strength: vi.fn(),
  })),
  zoom: vi.fn(() => ({
    scaleExtent: vi.fn(() => ({
      on: vi.fn(),
    })),
  })),
  drag: vi.fn(() => ({
    on: vi.fn(() => ({
      on: vi.fn(() => ({
        on: vi.fn(),
      })),
    })),
  })),
}))

const mockNetworkData = {
  categories: [
    { id: 'business-strategy', name: 'Business Strategy & Brand Differentiation', status: 'idle', progress: 0 },
    { id: 'omnichannel', name: 'Omnichannel & Transformation', status: 'running', progress: 45 },
    { id: 'content-creation', name: 'Content Creation & Delivery', status: 'idle', progress: 0 }
  ],
  agents: [
    { id: 'clinical-profile', name: 'Clinical Profile Agent', category: 'business-strategy', status: 'idle', progress: 0 },
    { id: 'therapeutic-overview', name: 'Therapeutic Overview', category: 'business-strategy', status: 'completed', progress: 100 },
    { id: 'market-research', name: 'Market Research Agent', category: 'business-strategy', status: 'running', progress: 65 }
  ]
}

describe('AgentNetworkGraph', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Mock successful API response
    mockedAxios.get.mockResolvedValue({
      data: {
        success: true,
        data: mockNetworkData
      }
    })
  })

  afterEach(() => {
    vi.clearAllTimers()
  })

  it('renders loading state initially', () => {
    render(<AgentNetworkGraph />)
    expect(screen.getByText('Loading agent network...')).toBeInTheDocument()
  })

  it('fetches network data on mount', async () => {
    render(<AgentNetworkGraph />)
    
    await waitFor(() => {
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/agents/network')
    })
  })

  it('renders SVG element after loading', async () => {
    render(<AgentNetworkGraph />)
    
    await waitFor(() => {
      expect(screen.queryByText('Loading agent network...')).not.toBeInTheDocument()
    })

    const svg = document.querySelector('svg.agent-network-svg')
    expect(svg).toBeInTheDocument()
  })

  it('handles API error gracefully', async () => {
    mockedAxios.get.mockRejectedValue(new Error('API Error'))
    
    render(<AgentNetworkGraph />)
    
    await waitFor(() => {
      expect(screen.queryByText('Loading agent network...')).not.toBeInTheDocument()
    })

    // Should still render SVG with fallback data
    const svg = document.querySelector('svg.agent-network-svg')
    expect(svg).toBeInTheDocument()
  })

  it('sets up periodic polling', async () => {
    vi.useFakeTimers()
    
    render(<AgentNetworkGraph />)
    
    // Initial call
    await waitFor(() => {
      expect(mockedAxios.get).toHaveBeenCalledTimes(1)
    })

    // Fast-forward 5 seconds
    vi.advanceTimersByTime(5000)
    
    await waitFor(() => {
      expect(mockedAxios.get).toHaveBeenCalledTimes(2)
    })

    vi.useRealTimers()
  })

  it('shows refresh indicator during updates', async () => {
    vi.useFakeTimers()
    
    render(<AgentNetworkGraph />)
    
    // Wait for initial load
    await waitFor(() => {
      expect(screen.queryByText('Loading agent network...')).not.toBeInTheDocument()
    })

    // Trigger refresh
    vi.advanceTimersByTime(5000)
    
    // Should show refresh indicator briefly
    expect(screen.getByText('Updating...')).toBeInTheDocument()

    vi.useRealTimers()
  })

  it('handles visibility change events', async () => {
    const addEventListenerSpy = vi.spyOn(document, 'addEventListener')
    const removeEventListenerSpy = vi.spyOn(document, 'removeEventListener')
    
    const { unmount } = render(<AgentNetworkGraph />)
    
    expect(addEventListenerSpy).toHaveBeenCalledWith('visibilitychange', expect.any(Function))
    
    unmount()
    
    expect(removeEventListenerSpy).toHaveBeenCalledWith('visibilitychange', expect.any(Function))
  })
})
