.agent-network-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 50%, #cbd5e1 100%);
  z-index: -1;
  overflow: hidden;
}

.agent-network-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 30%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.agent-network-svg {
  width: 100%;
  height: 100%;
  display: block;
}

.node {
  transition: all 0.2s ease;
}

.node:hover {
  filter: brightness(1.1);
}

.node circle {
  transition: all 0.2s ease;
}

.node text {
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  user-select: none;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Tooltip styles */
.agent-tooltip {
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Animation for progress rings */
.node circle[stroke-dasharray] {
  transition: stroke-dasharray 0.5s ease;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .node text {
    font-size: 6px !important;
  }

  .node circle {
    r: 20px;
  }

  .node circle[r="40"] {
    r: 30px;
  }
}

/* Loading indicator styles */
.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #64748b;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

/* Animations */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* Refresh indicator */
.refresh-indicator {
  position: absolute;
  top: 20px;
  right: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 8px 16px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: 'Montserrat', sans-serif;
  font-size: 12px;
  color: #64748b;
  z-index: 10;
}

.refresh-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Agent detail panel */
.agent-detail-panel {
  font-family: 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif !important;
  animation: fadeInScale 0.2s ease-out;
}

.agent-detail-panel .close-button:hover {
  color: #374151 !important;
  transform: scale(1.1);
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .agent-detail-panel {
    max-width: 90vw !important;
    padding: 16px !important;
  }

  .refresh-indicator {
    top: 10px !important;
    right: 10px !important;
    padding: 6px 12px !important;
    font-size: 11px !important;
  }

  .agent-tooltip {
    font-size: 12px !important;
    padding: 6px 8px !important;
  }
}

@media (max-width: 480px) {
  .agent-detail-panel {
    max-width: 95vw !important;
    padding: 12px !important;
  }

  .agent-detail-panel h3 {
    font-size: 16px !important;
  }
}

/* Performance optimizations */
.agent-network-svg {
  will-change: transform;
}

.agent-network-svg circle {
  will-change: transform;
}

.agent-network-svg text {
  will-change: transform;
}
