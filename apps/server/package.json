{"name": "server", "version": "1.0.0", "description": "Express + Node.js backend with SQLite database", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "init-db": "node src/database/init.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest tests/integration", "test:unit": "jest tests/unit"}, "keywords": ["express", "nodejs", "sqlite", "api"], "author": "<PERSON>", "license": "ISC", "dependencies": {"@types/d3": "^7.4.3", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "d3": "^7.9.0", "dotenv": "^16.5.0", "express": "^4.21.2", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "sqlite3": "^5.1.7", "uuid": "^11.1.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^7.1.1"}}