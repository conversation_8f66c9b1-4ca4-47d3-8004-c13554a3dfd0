const database = require('../connection');

async function addBrandingColumns() {
  try {
    console.log('Starting branding columns migration...');
    
    // Connect to database
    await database.connect();
    
    // Check if the new columns exist in Brands table
    const tableInfo = await database.all("PRAGMA table_info(Brands)");
    const hasDesignSystem = tableInfo.some(column => column.name === 'design_system');
    const hasVoice = tableInfo.some(column => column.name === 'voice');
    const hasTone = tableInfo.some(column => column.name === 'tone');
    const hasMedical = tableInfo.some(column => column.name === 'medical');
    
    // Add design_system column if it doesn't exist
    if (!hasDesignSystem) {
      console.log('Adding design_system column to Brands table...');
      await database.run(`
        ALTER TABLE Brands 
        ADD COLUMN design_system TEXT
      `);
    }
    
    // Add voice column if it doesn't exist
    if (!hasVoice) {
      console.log('Adding voice column to Brands table...');
      await database.run(`
        ALTER TABLE Brands 
        ADD COLUMN voice TEXT
      `);
    }
    
    // Add tone column if it doesn't exist
    if (!hasTone) {
      console.log('Adding tone column to Brands table...');
      await database.run(`
        ALTER TABLE Brands 
        ADD COLUMN tone TEXT
      `);
    }
    
    // Add medical column if it doesn't exist
    if (!hasMedical) {
      console.log('Adding medical column to Brands table...');
      await database.run(`
        ALTER TABLE Brands 
        ADD COLUMN medical TEXT
      `);
    }
    
    console.log('Branding columns migration completed successfully');
    
  } catch (error) {
    console.error('Error during branding columns migration:', error);
    throw error;
  } finally {
    await database.close();
  }
}

// Run migration if called directly
if (require.main === module) {
  addBrandingColumns()
    .then(() => {
      console.log('Branding columns migration finished');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Branding columns migration failed:', error);
      process.exit(1);
    });
}

module.exports = { addBrandingColumns };
