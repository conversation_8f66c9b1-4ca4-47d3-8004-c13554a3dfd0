import React, { useEffect, useRef, useState } from 'react';
import * as d3 from 'd3';
import './AgentNetworkGraph.css';

interface AgentNode {
  id: string;
  name: string;
  category: string;
  type: 'category' | 'agent';
  status: 'idle' | 'running' | 'completed' | 'error';
  progress: number; // 0-100
  x?: number;
  y?: number;
  fx?: number | null;
  fy?: number | null;
}

interface AgentLink {
  source: string | AgentNode;
  target: string | AgentNode;
}

const AgentNetworkGraph: React.FC = () => {
  const svgRef = useRef<SVGSVGElement>(null);
  const [dimensions, setDimensions] = useState({ width: 800, height: 600 });
  const [animationFrame, setAnimationFrame] = useState(0);

  // Define the agent data structure
  const agentData: AgentNode[] = [
    // Category nodes (larger, central)
    { id: 'business-strategy', name: 'Business Strategy & Brand Differentiation', category: 'business-strategy', type: 'category', status: 'idle', progress: 0 },
    { id: 'omnichannel', name: 'Omnichannel & Transformation', category: 'omnichannel', type: 'category', status: 'idle', progress: 0 },
    { id: 'content-creation', name: 'Content Creation & Delivery', category: 'content-creation', type: 'category', status: 'idle', progress: 0 },
    
    // Business Strategy agents
    { id: 'clinical-profile', name: 'Clinical Profile Agent', category: 'business-strategy', type: 'agent', status: 'idle', progress: 0 },
    { id: 'therapeutic-overview', name: 'Therapeutic Overview', category: 'business-strategy', type: 'agent', status: 'completed', progress: 100 },
    { id: 'market-research', name: 'Market Research Agent', category: 'business-strategy', type: 'agent', status: 'running', progress: 65 },
    { id: 'product-profile', name: 'Product Profile Agent', category: 'business-strategy', type: 'agent', status: 'completed', progress: 100 },
    { id: 'customer-profile', name: 'Customer Profile Agent', category: 'business-strategy', type: 'agent', status: 'idle', progress: 0 },
    { id: 'brand-positioning', name: 'Brand Positioning Agent', category: 'business-strategy', type: 'agent', status: 'error', progress: 25 },
    
    // Omnichannel agents
    { id: 'patient-journey', name: 'Patient Journey Agent', category: 'omnichannel', type: 'agent', status: 'idle', progress: 0 },
    { id: 'experience-arc', name: 'Experience Arc Agent', category: 'omnichannel', type: 'agent', status: 'running', progress: 40 },
    { id: 'customer-segments', name: 'Customer Segments Agent', category: 'omnichannel', type: 'agent', status: 'idle', progress: 0 },
    { id: 'message-map', name: 'Message Map Agent', category: 'omnichannel', type: 'agent', status: 'idle', progress: 0 },
    { id: 'channel-mix', name: 'Channel Mix Agent', category: 'omnichannel', type: 'agent', status: 'idle', progress: 0 },
    { id: 'content-plan', name: 'Content Plan Agent', category: 'omnichannel', type: 'agent', status: 'idle', progress: 0 },
    { id: 'content-module', name: 'Content Module Agent', category: 'omnichannel', type: 'agent', status: 'idle', progress: 0 },
    
    // Content Creation agents
    { id: 'production-intake', name: 'Production Intake Agent', category: 'content-creation', type: 'agent', status: 'idle', progress: 0 },
    { id: 'create-email', name: 'Create Email Agent', category: 'content-creation', type: 'agent', status: 'idle', progress: 0 },
    { id: 'validate-output', name: 'Validate Output Agent', category: 'content-creation', type: 'agent', status: 'idle', progress: 0 },
    { id: 'deploy', name: 'Deploy Agent', category: 'content-creation', type: 'agent', status: 'idle', progress: 0 },
  ];

  // Create links between category nodes and their agents
  const links: AgentLink[] = [
    // Business Strategy links
    ...agentData.filter(d => d.category === 'business-strategy' && d.type === 'agent')
      .map(d => ({ source: 'business-strategy', target: d.id })),
    
    // Omnichannel links
    ...agentData.filter(d => d.category === 'omnichannel' && d.type === 'agent')
      .map(d => ({ source: 'omnichannel', target: d.id })),
    
    // Content Creation links
    ...agentData.filter(d => d.category === 'content-creation' && d.type === 'agent')
      .map(d => ({ source: 'content-creation', target: d.id })),
  ];

  // Color schemes for different categories
  const getNodeColor = (node: AgentNode) => {
    const colors = {
      'business-strategy': '#10B981', // Green
      'omnichannel': '#3B82F6',      // Blue  
      'content-creation': '#F59E0B'   // Orange
    };
    return colors[node.category as keyof typeof colors] || '#6B7280';
  };

  const getStatusColor = (status: string) => {
    const statusColors = {
      'idle': '#E5E7EB',
      'running': '#3B82F6',
      'completed': '#10B981',
      'error': '#EF4444'
    };
    return statusColors[status as keyof typeof statusColors] || '#E5E7EB';
  };

  // Animation effect for running agents
  useEffect(() => {
    const interval = setInterval(() => {
      setAnimationFrame(prev => prev + 1);
    }, 2000);
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const handleResize = () => {
      const container = svgRef.current?.parentElement;
      if (container) {
        setDimensions({
          width: container.clientWidth,
          height: container.clientHeight
        });
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    if (!svgRef.current) return;

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    const { width, height } = dimensions;
    
    // Create simulation with better forces for horizontal grouping
    const simulation = d3.forceSimulation(agentData)
      .force('link', d3.forceLink(links).id((d: any) => d.id).distance(80))
      .force('charge', d3.forceManyBody().strength((d: any) => d.type === 'category' ? -800 : -200))
      .force('center', d3.forceCenter(width / 2, height / 2))
      .force('collision', d3.forceCollide().radius((d: any) => d.type === 'category' ? 60 : 35))
      .force('x', d3.forceX().strength(0.1))
      .force('y', d3.forceY().strength(0.1));

    // Create container group
    const container = svg.append('g');

    // Add zoom behavior
    const zoom = d3.zoom()
      .scaleExtent([0.5, 3])
      .on('zoom', (event) => {
        container.attr('transform', event.transform);
      });

    svg.call(zoom as any);

    // Create links
    const link = container.append('g')
      .selectAll('line')
      .data(links)
      .enter().append('line')
      .attr('stroke', '#E5E7EB')
      .attr('stroke-width', 2)
      .attr('stroke-opacity', 0.6);

    // Create node groups
    const node = container.append('g')
      .selectAll('g')
      .data(agentData)
      .enter().append('g')
      .attr('class', 'node')
      .style('cursor', 'pointer');

    // Add drop shadow filter
    const defs = svg.append('defs');
    const filter = defs.append('filter')
      .attr('id', 'drop-shadow')
      .attr('x', '-50%')
      .attr('y', '-50%')
      .attr('width', '200%')
      .attr('height', '200%');

    filter.append('feDropShadow')
      .attr('dx', 2)
      .attr('dy', 2)
      .attr('stdDeviation', 3)
      .attr('flood-opacity', 0.3);

    // Add circles for nodes
    node.append('circle')
      .attr('r', (d: AgentNode) => d.type === 'category' ? 40 : 25)
      .attr('fill', (d: AgentNode) => getNodeColor(d))
      .attr('stroke', '#fff')
      .attr('stroke-width', 2)
      .attr('filter', 'url(#drop-shadow)')
      .attr('class', (d: AgentNode) => d.status === 'running' ? 'pulsing' : '');

    // Add pulsing animation for running agents
    const style = svg.append('style');
    style.text(`
      .pulsing {
        animation: pulse 2s infinite;
      }
      @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.6; }
        100% { opacity: 1; }
      }
    `);

    // Add progress rings
    node.append('circle')
      .attr('r', (d: AgentNode) => d.type === 'category' ? 42 : 27)
      .attr('fill', 'none')
      .attr('stroke', (d: AgentNode) => getStatusColor(d.status))
      .attr('stroke-width', 3)
      .attr('stroke-dasharray', (d: AgentNode) => {
        const radius = d.type === 'category' ? 42 : 27;
        const circumference = 2 * Math.PI * radius;
        const progress = circumference * (d.progress / 100);
        return `${progress} ${circumference - progress}`;
      })
      .attr('stroke-dashoffset', 0)
      .style('transform', 'rotate(-90deg)')
      .style('transform-origin', 'center');

    // Add text labels
    node.append('text')
      .text((d: AgentNode) => {
        // Truncate long names for better display
        const maxLength = d.type === 'category' ? 12 : 8;
        return d.name.length > maxLength ? d.name.substring(0, maxLength) + '...' : d.name;
      })
      .attr('text-anchor', 'middle')
      .attr('dy', '0.35em')
      .attr('font-size', (d: AgentNode) => d.type === 'category' ? '10px' : '8px')
      .attr('font-weight', (d: AgentNode) => d.type === 'category' ? 'bold' : 'normal')
      .attr('fill', 'white')
      .style('pointer-events', 'none');

    // Add drag behavior
    const drag = d3.drag<SVGGElement, AgentNode>()
      .on('start', (event, d) => {
        if (!event.active) simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
      })
      .on('drag', (event, d) => {
        d.fx = event.x;
        d.fy = event.y;
      })
      .on('end', (event, d) => {
        if (!event.active) simulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;
      });

    node.call(drag);

    // Add hover effects
    node.on('mouseover', function(event, d) {
      d3.select(this).select('circle').attr('stroke-width', 4);
      
      // Show tooltip
      const tooltip = d3.select('body').append('div')
        .attr('class', 'agent-tooltip')
        .style('opacity', 0)
        .style('position', 'absolute')
        .style('background', 'rgba(0, 0, 0, 0.8)')
        .style('color', 'white')
        .style('padding', '8px')
        .style('border-radius', '4px')
        .style('font-size', '12px')
        .style('pointer-events', 'none')
        .style('z-index', '1000');

      tooltip.transition()
        .duration(200)
        .style('opacity', 1);

      tooltip.html(`
        <strong>${d.name}</strong><br/>
        Status: ${d.status}<br/>
        Progress: ${d.progress}%
      `)
        .style('left', (event.pageX + 10) + 'px')
        .style('top', (event.pageY - 10) + 'px');
    })
    .on('mouseout', function() {
      d3.select(this).select('circle').attr('stroke-width', 2);
      d3.selectAll('.agent-tooltip').remove();
    });

    // Update positions on simulation tick
    simulation.on('tick', () => {
      link
        .attr('x1', (d: any) => d.source.x)
        .attr('y1', (d: any) => d.source.y)
        .attr('x2', (d: any) => d.target.x)
        .attr('y2', (d: any) => d.target.y);

      node
        .attr('transform', (d: AgentNode) => `translate(${d.x},${d.y})`);
    });

    // Position category nodes horizontally after initial simulation
    setTimeout(() => {
      const categoryNodes = agentData.filter(d => d.type === 'category');
      const spacing = width / (categoryNodes.length + 1);

      categoryNodes.forEach((node, i) => {
        node.fx = spacing * (i + 1);
        node.fy = height / 2;
      });

      // Position agent nodes around their category centers
      agentData.filter(d => d.type === 'agent').forEach(agent => {
        const categoryNode = categoryNodes.find(cat => cat.id === agent.category);
        if (categoryNode) {
          const categoryIndex = categoryNodes.indexOf(categoryNode);
          const categoryX = spacing * (categoryIndex + 1);

          // Add some randomness around the category center
          const angle = Math.random() * 2 * Math.PI;
          const distance = 80 + Math.random() * 60;
          agent.fx = categoryX + Math.cos(angle) * distance;
          agent.fy = height / 2 + Math.sin(angle) * distance;
        }
      });

      simulation.alpha(0.5).restart();

      // Release fixed positions after a short time to allow natural movement
      setTimeout(() => {
        agentData.forEach(node => {
          if (node.type === 'agent') {
            node.fx = null;
            node.fy = null;
          }
        });
      }, 2000);
    }, 1000);

  }, [dimensions]);

  return (
    <div className="agent-network-container">
      <svg
        ref={svgRef}
        width={dimensions.width}
        height={dimensions.height}
        className="agent-network-svg"
      />
    </div>
  );
};

export default AgentNetworkGraph;
