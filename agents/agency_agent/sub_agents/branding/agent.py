"""Defines the Branding Agent which creates brand identity elements for products."""

from google.adk.agents.llm_agent import Agent
from google.adk.tools.agent_tool import AgentTool
from ...shared_libraries import constants
from ...tools.datetime_tools import get_current_day_of_week_tool
from ...tools.storage_tools import upload_str_gcs_tool, download_str_gcs_tool
from .prompts.instructions import AGENT_PROMPT
from .prompts.review import REVIEW_PROMPT

branding_review_agent = Agent(
    model=constants.MODEL,
    name="branding_review_agent",
    description="A helpful agent that reviews the output of branding_agent.",
    output_key="branding_review_agent_output",
    instruction=REVIEW_PROMPT,
    tools=[]
)

branding_agent = Agent(
    model=constants.MODEL,
    name="branding_agent",
    description="A helpful agent that creates comprehensive branding elements for products including brand colors, fonts, and logos.",
    output_key=f"branding_agent_output",
    instruction=AGENT_PROMPT,
    tools=[
        AgentTool(branding_review_agent),
        get_current_day_of_week_tool,
        upload_str_gcs_tool,
        download_str_gcs_tool,
    ]
)
